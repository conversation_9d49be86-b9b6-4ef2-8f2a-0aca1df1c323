@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
    --foreground-rgb: 0, 0, 0;
    --background-start-rgb: 255, 255, 255;
    --background-end-rgb: 255, 255, 255;

    /* Theme variables */
    --primary-color: #fd7e14;
    --primary-light-color: #fd7d1447;
    --primary-hover-color: #e67212;
    --secondary-color: #343a40;
    --accent-color: #e0e0e0;
    --border-color: #e0e0e0;
    --background-color: #ffffff;
    --text-color: #000000;
    --text-primary-color: #343a40;
    --text-secondary-color: #1b1f3b66;
}

@media (prefers-color-scheme: dark) {
    :root {
        --foreground-rgb: 0, 0, 0;
        --background-start-rgb: 255, 255, 255;
        --background-end-rgb: 255, 255, 255;
        /* Dark theme variables would go here if needed */
    }
}

body {
    color: rgb(var(--foreground-rgb));
    background: linear-gradient(
            to bottom,
            transparent,
            rgb(var(--background-end-rgb))
        )
        rgb(var(--background-start-rgb));
    padding: 0;
    margin: 0;
}

@layer utilities {
    .text-balance {
        text-wrap: balance;
    }
}

/* custom scrollbars */
.scrollbar::-webkit-scrollbar {
    width: 5px;
    height: 100%;
}

.scrollbar::-webkit-scrollbar-thumb {
    background-color: #d1d1d1c1;
    border-radius: 3px;
}

.scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: #ababab;
}

.scrollbar-mini::-webkit-scrollbar {
    width: 2px;
    height: 100%;
}

.scrollbar-mini::-webkit-scrollbar-thumb {
    background-color: #d1d1d18f;
    border-radius: 3px;
}

.scrollbar-mini::-webkit-scrollbar-thumb:hover {
    background-color: #ababab;
}

.scrollbar-hide::-webkit-scrollbar {
    width: 0px;
    height: 100%;
}

/* Antd Modal */
/* .ant-modal-content {
  border-radius: 20px !important;
} */

.custome-selector .ant-select-selector {
    border-radius: 6px !important;
    box-shadow: none !important;
    border-color: var(--border-color) !important;
    height: 36px !important;
    margin: 0 !important;
}

.custome-selector .ant-select-selection-item {
    font-size: 16px !important;
    line-height: 20px !important;
    font-weight: 500 !important;
}

.ant-select-selector {
    border-radius: 12px !important;
    box-shadow: none !important;
    border-color: var(--border-color) !important;
}
.ant-select-selector:hover {
    border-color: var(--primary-color) !important;
}

/* .ant-select-open{
  border: 1px solid var(--primary-color) !important;
} */

.ant-select-selection-item {
    font-size: 12px !important;
    line-height: 20px !important;
    font-weight: 400 !important;
}

.ant-select-item-option-selected {
    background-color: var(--primary-light-color) !important;
}

.ant-popover-inner {
    padding: 0 !important;
}

/* Share Modal */
.share-modal .ant-modal-content {
    border-radius: 12px;
    overflow: hidden;
}

.share-modal .ant-modal-header {
    padding: 5px 24px 15px 24px;
    border-bottom: 1px solid #f0f0f0;
}

.share-modal .ant-modal-body {
    padding: 10px 14px;
}

/* Custom Slider */
.custom-slider .ant-slider-track {
  background-color: var(--primary-color) !important;
}

.custom-slider .ant-slider-handle {
  background-color: white !important;
  border: 2px solid var(--primary-color) !important;
  box-shadow: none !important;
  width: 12px !important;
  height: 12px !important;
}

.custom-slider .ant-slider-handle::after {
  display: none !important;
}

.custom-slider .ant-slider-handle:hover {
  border-color: var(--primary-hover-color) !important;
}

.custom-slider .ant-slider-handle:focus {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 2px rgba(253, 126, 20, 0.2) !important;
}

/* Playlist-popup ui */
.playlist-popup .ant-segmented-item-selected, .playlist-popup .ant-segmented-thumb{
    background-color: var(--primary-color);
    color: var(--background-color);
    border-radius: 10px;
}

.playlist-popup .ant-segmented-item{
    border-radius: 10px !important;
    margin-left: 1px !important;
    margin-right: 1px !important;
}

/* Marquee animation for continuous scrolling */
@keyframes marquee {
    0% {
        transform: translate3d(100%, 0, 0);
    }
    100% {
        transform: translate3d(-100%, 0, 0);
    }
}

.animate-marquee {
    will-change: transform;
}

/* Antd switch loader color */
.ant-switch-loading-icon {
  color: var(--primary-color) !important;
}
.marquee {
  overflow: hidden;
  white-space: nowrap;
  position: relative;
}

.marquee-content {
  display: inline-block;
  padding-left: 100%;
  animation: marquee 10s linear infinite;
}

@keyframes marquee {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-100%);
  }
}