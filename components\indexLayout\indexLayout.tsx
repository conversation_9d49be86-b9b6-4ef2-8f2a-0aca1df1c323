"use client";

import { usePathname, useRouter } from "next/navigation";
import React, { useEffect, useState, useCallback, useRef, use } from "react";
import Navbar from "../navbar/navbar";
import Sidebar from "../sidebar/sidebar";
import LogoutModal from "../Modal/logoutModal";
import AudioPlayer from "../audioPlayer/audioPlayer";
import AOS from "aos";
import "aos/dist/aos.css";
import { useSidebarContext } from "@/src/context/sidebar.context";
import ThemeProvider from "../ThemeProvider";
import { loadThemeVariables } from "@/src/utils/themeLoader";
import { useIndexedDBContext } from "@/src/context/indexedDB.context";
import FullScreenLoader from "../ui/fullScreenLoader";
import TabLoader from "../ui/tabLoader";
import { waitForAuthState } from "@/src/libs/helper";
import { onMessage } from "firebase/messaging";
import { messaging } from "@/src/config/firebase.config";

const NAVBAR_HEIGHT = 58; // Navbar height in pixels
const SIDEBAR_WIDTH = 230; // Sidebar width in pixels

const fullPagePaths = ["/login", "/register", "/restore"];
const appPagePaths = [
    "/media-library",
    "/popular",
    "/favorites",
    "/playlists",
    "/history",
    "/knowledge-base",
    "/statistics",
    "/about",
    "/donate",
    "/settings",
    "/video",
    "/transcription",
];

const IndexLayout = ({ children }: { children: React.ReactNode }) => {
    const router = useRouter();
    const pathName = usePathname();
    const { isCollapsed, isTabChangeLoading, setIsTabChangeLoading } =
        useSidebarContext();
    const [height, setHeight] = useState(0);
    const { syncData, syncLectureInfoData, isSyncing, lastSyncTime } =
        useIndexedDBContext();
    const [isLoading, setIsLoading] = useState(true);
    const [isAuthChecked, setIsAuthChecked] = useState(false);

    // Use refs to track if we've already synced during this session
    const hasSyncedRef = useRef(false);
    const hasLectureInfoSyncedRef = useRef(false);

    // Minimum time between syncs (in milliseconds) - 1 hour
    const MIN_SYNC_INTERVAL = 60 * 60 * 1000;

    // Update height and handle resize efficiently
    const handleResize = useCallback(() => setHeight(window.innerHeight), []);

    const firstRender = useRef<any>(false);

    useEffect(() => {
        onMessage(messaging, (payload) => {
            console.log("Foreground message received:", payload);
            const { title, body }: any = payload.notification;
            new Notification(title, { body });
        });
    }, [])

    useEffect(() => {
        const checkAuth = async () => {
            const user = await waitForAuthState();

            if (fullPagePaths.includes(pathName)) {
                if (user) {
                    router.replace("/media-library"); // redirect logged-in user away from login pages
                }
            } else {
                if (!user) {
                    window.location.href = "/login"; // redirect unauthenticated user to login
                }
            }

            setIsAuthChecked(true); // auth status determined
        };

        if (!firstRender.current) {
            checkAuth();
            firstRender.current = true;
        }
    }, [pathName]);

    useEffect(() => {
        // Set initial height
        setHeight(window.innerHeight);

        // Add resize event listener
        window.addEventListener("resize", handleResize);

        // Cleanup event listener on unmount
        return () => window.removeEventListener("resize", handleResize);
    }, [handleResize]);

    useEffect(() => {
        // Initialize AOS animation library
        AOS.init({
            duration: 500,
            once: true,
            mirror: false,
        });

        // Load theme variables
        loadThemeVariables();
    }, []);

    useEffect(() => {
        const handlePopState = (event: PopStateEvent) => {
            // Callback logic for browser back/forward button
            // console.log("Browser navigation detected:", event);
            // You can add custom logic here, e.g., analytics, state updates, etc.
            setIsTabChangeLoading(true);
        };

        window.addEventListener("popstate", handlePopState);

        return () => {
            window.removeEventListener("popstate", handlePopState);
        };
    }, []);

    // Sync data when component mounts - only once per session and with time constraints
    useEffect(() => {
        // Only run this effect once when the component mounts
        if (hasSyncedRef.current) return;

        // Check if user is logged in
        const idToken = localStorage.getItem("idToken");

        // Only sync if:
        // 1. User is logged in
        // 2. Not currently syncing
        // 3. Either no previous sync or enough time has passed since last sync
        const shouldSync = idToken && !isSyncing;

        if (shouldSync) {
            console.log("Starting sync operation...");
            hasSyncedRef.current = true;

            // Start syncing lectures in the background
            syncData()
                .then(() => {
                    setIsLoading(false);
                })
                .catch((error) => {
                    setIsLoading(false);
                    console.error("Error syncing lectures:", error);
                });
        } else if (!idToken) {
            router.push("/login");
            setIsLoading(false);
        } else {
            setIsLoading(false);
        }
    }, [syncData, isSyncing, lastSyncTime, MIN_SYNC_INTERVAL]);

    // Always sync lectureInfo on page refresh to ensure we have the latest data
    useEffect(() => {
        // Only run this effect once when the component mounts
        if (hasLectureInfoSyncedRef.current) return;

        // Check if user is logged in
        const idToken = localStorage.getItem("idToken");

        // Only sync lectureInfo if user is logged in and not currently syncing
        if (idToken && !isSyncing) {
            console.log("Starting lectureInfo sync operation...");
            hasLectureInfoSyncedRef.current = true;

            // Start syncing lectureInfo in the background
            // This will update any changed fields in the local lectures from Firebase
            syncLectureInfoData().catch((error) => {
                console.error("Error syncing lectureInfo:", error);
            });
        }
    }, [syncLectureInfoData, isSyncing]);

    useEffect(() => {
        setTimeout(() => {
            setIsTabChangeLoading(false);
        }, 1000);
    }, [pathName]);

    if (isLoading || !isAuthChecked) {
        return <FullScreenLoader visible={true} />;
    }

    // Check for routes that need full-page layout
    if (fullPagePaths.includes(pathName)) {
        return (
            <ThemeProvider>
                <div
                    className="w-[100vw] !tracking-wide"
                    style={{
                        height: `${height}px`,
                    }}
                >
                    {isTabChangeLoading ? (
                        <FullScreenLoader visible={isLoading} />
                    ) : (
                        <>{children}</>
                    )}
                </div>
            </ThemeProvider>
        );
    }

    // Check for routes with the app layout
    if (appPagePaths.some((path) => pathName.startsWith(path))) {
        return (
            <ThemeProvider>
                <div
                    className="w-[100vw] !tracking-wide"
                    style={{
                        height: `${height}px`,
                    }}
                >
                    <Navbar />
                    <div className="w-full h-[calc(100%-58px)] flex relative">
                        <Sidebar />
                        {isTabChangeLoading ? (
                            <div
                                className={`w-full ${isCollapsed
                                    ? "lg:w-[calc(100%-68px)]"
                                    : "lg:w-[calc(100%-230px)]"
                                    } transition-all duration-500 h-full`}
                                style={{
                                    height: `calc(${height}px - ${NAVBAR_HEIGHT}px)`,
                                }}
                            >
                            <TabLoader visible={isTabChangeLoading} />
                            </div>
                        ) : (
                            <div
                                className={`w-full ${isCollapsed
                                    ? "lg:w-[calc(100%-68px)]"
                                    : "lg:w-[calc(100%-230px)]"
                                    } transition-all duration-500 h-full`}
                                style={{
                                    height: `calc(${height}px - ${NAVBAR_HEIGHT}px)`,
                                }}
                            >
                                {children}
                            </div>
                        )}
                    </div>
                    <AudioPlayer />
                </div>
            </ThemeProvider>
        );
    }

    // Default rendering for non-matching routes
    return (
        <ThemeProvider>
            {children}
            <AudioPlayer />
        </ThemeProvider>
    );
};

export default IndexLayout;
