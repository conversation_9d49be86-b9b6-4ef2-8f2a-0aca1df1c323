"use client";
import React from "react";
import dynamic from "next/dynamic";

// Dynamically import ApexCharts to avoid SSR issues
const Chart = dynamic(() => import("react-apexcharts"), { ssr: false });

interface HalfCircleProgressChartProps {
    percentage: number;
    size?: number;
    showPercentage?: boolean;
}

const HalfCircleProgressChart: React.FC<HalfCircleProgressChartProps> = ({
    percentage,
    size = 64,
    showPercentage = true,
}) => {
    const chartOptions = {
        chart: {
            type: "radialBar" as const,
            height: size,
            width: size,
            sparkline: {
                enabled: true,
            },
        },
        plotOptions: {
            radialBar: {
                startAngle: -90,
                endAngle: 90,
                hollow: {
                    size: "60%",
                },
                track: {
                    background: "var(--primary-light-color)",
                    strokeWidth: "100%",
                    margin: 0,
                },
                dataLabels: {
                    show: showPercentage,
                    name: {
                        show: false,
                    },
                    value: {
                        show: showPercentage,
                        fontSize: "14px",
                        fontWeight: 600,
                        color: "var(--primary-color)",
                        offsetY: 10,
                        formatter: function (val: number) {
                            return Math.round(val) + "%";
                        },
                    },
                },
            },
        },
        fill: {
            type: "gradient",
            gradient: {
                shade: "light",
                type: "horizontal",
                shadeIntensity: 0.5,
                gradientToColors: ["#87D4F9"],
                inverseColors: false,
                opacityFrom: 1,
                opacityTo: 1,
                stops: [0, 100],
                colorStops: [
                    {
                        offset: 0,
                        color: "var(--primary-color)",
                        opacity: 1,
                    },
                ],
            },
        },
        stroke: {
            lineCap: "round" as const,
        },
        labels: ["Progress"],
    };

    const chartSeries = [Math.min(100, Math.max(0, percentage))];

    return (
        <div className="flex items-center justify-center">
            {typeof window !== "undefined" && (
                <Chart
                    options={chartOptions}
                    series={chartSeries}
                    type="radialBar"
                    height={size}
                    width={size}
                />
            )}
        </div>
    );
};

export default HalfCircleProgressChart;
