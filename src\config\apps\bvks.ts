/**
 * <PERSON><PERSON><PERSON> (<PERSON><PERSON>) specific configuration
 */

import { defaultConfig } from './default';

export const bvksConfig = {
  ...defaultConfig,
  
  // App metadata
  appName: 'BVKS',
  windowTitle: '<PERSON><PERSON>',
  appTitle: '<PERSON><PERSON>',
  appDescription: '<PERSON><PERSON>',
  favicon: "/favicon/bvks/favicon.ico",
  appleTouchIcon: "/favicon/bvks/apple-touch-icon.png",
  favicon16: "/favicon/bvks/favicon-16x16.png",
  favicon32: "/favicon/bvks/favicon-32x32.png",
  manifest: "/favicon/bvks/site.webmanifest",

  // BVKS specific URLs
  defaultLectureThumbnail: 'https://cdn.bvksmedia.com/wp-content/uploads/2018/05/20151503/Untitled-2.jpg',
  // defaultLectureThumbnail: '/images/ui/defaultLectureThumbnail.jpg',
  // defaultPlaylistThumbnail: 'https://bvksmedia.com/assets/img/playlist_thumbnail.jpg',
  defaultPlaylistThumbnail: '/images/bvks/playlist_thumbnail.jpg',

  // BVKS Notification Topics
  topics: ["BVKS_GENERAL", "BVKS_ENGLISH", "BVKS_HINDI", "BVKS_BENGALI"],

  privacyPolicy: "https://bvks.com/privacy-policy-2/",
  cookiePolicy: "https://bvks-d1ac.kxcdn.com/wp-content/uploads/2021/12/02220746/Cookie-Policy.pdf",
  
  // Theme colors - using the existing orange theme
  theme: {
    ...defaultConfig.theme,
    primary: '#fd7e14',       // Primary color (orange)
    primaryLight: '#fd7d1414', // Light version of primary color
    primaryHover: '#fd7d143c',  // Darker version for hover states
    textPrimary: '#343A40',       // Primary color (orange)
  },
  
  // Feature flags specific to BVKS
  features: {
    ...defaultConfig.features,
    // Add any BVKS-specific feature flags here
  }
};

export default bvksConfig;
